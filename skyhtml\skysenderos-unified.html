<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>SkySenderos - Unified Survey Management</title>
  <style>
    body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    color: #333;
    }

    .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    }

    h1 {
    text-align: center;
    color: #585c2c;
    font-size: 2.5rem;
    margin-bottom: 30px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    font-weight: 700;
    }

    nav {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 40px;
    flex-wrap: wrap;
    }

    nav button {
    background: linear-gradient(135deg, #585c2c 0%, #6a6f35 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 12px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(88, 92, 44, 0.3);
    }

    nav button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(88, 92, 44, 0.4);
    background: linear-gradient(135deg, #6a6f35 0%, #585c2c 100%);
    }

    nav button.active {
    background: linear-gradient(135deg, #4a5228 0%, #585c2c 100%);
    transform: translateY(-1px);
    }

    .page {
    display: none;
    animation: fadeIn 0.3s ease-in;
    }

    .page.active {
    display: block;
    }

    @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
    }

    /* Search container styling */
    .search-container {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    }

    .search-container input {
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    min-width: 250px;
    flex: 1;
    max-width: 400px;
    }

    .search-container input:focus {
    border-color: #585c2c;
    outline: none;
    }

    .search-container button {
    padding: 10px 20px;
    background: #585c2c;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s ease;
    }

    .search-container button:hover {
    background: #6a6f35;
    }

    .search-container button:last-child {
    background: #666;
    }

    .search-container button:last-child:hover {
    background: #777;
    }

    /* Form styling */
    form {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    margin-bottom: 30px;
    }

    form label {
    font-weight: 600;
    color: #585c2c;
    margin-bottom: 8px;
    display: block;
    }

    form input,
    form textarea,
    form select {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
    }

    form input:focus,
    form textarea:focus,
    form select:focus {
    border-color: #585c2c;
    outline: none;
    box-shadow: 0 0 0 3px rgba(88, 92, 44, 0.1);
    }

    form textarea {
    resize: vertical;
    min-height: 100px;
    }

    form button {
    grid-column: span 2;
    background: linear-gradient(135deg, #585c2c 0%, #6a6f35 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 12px;
    cursor: pointer;
    font-size: 18px;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-top: 20px;
    }

    form button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(88, 92, 44, 0.3);
    }

    /* Survey card styling */
    .survey-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    display: flex;
    gap: 25px;
    align-items: flex-start;
    }

    .survey-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(0,0,0,0.15);
    }

    .survey-info {
    flex: 1;
    }

    .survey-info p {
    margin: 8px 0;
    line-height: 1.6;
    }

    .survey-info strong {
    color: #585c2c;
    font-weight: 600;
    }

    /* Button styling */
    .edit-btn, .duplicate-btn, .reinstate-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    margin: 5px 5px 5px 0;
    transition: background-color 0.3s ease;
    }

    .edit-btn:hover {
    background: #0056b3;
    }

    .duplicate-btn {
    background: #28a745;
    }

    .duplicate-btn:hover {
    background: #218838;
    }

    .reinstate-btn {
    background: #28a745;
    }

    .reinstate-btn:hover {
    background: #218838;
    }

    button[onclick*="delete"] {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    margin: 5px 5px 5px 0;
    transition: background-color 0.3s ease;
    }

    button[onclick*="delete"]:hover {
    background: #c82333;
    }

    /* Archived badge */
    .archived-badge {
    display: inline-block;
    background: linear-gradient(45deg, #dc3545, #c82333);
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    margin-left: 10px;
    }

    /* Pagination controls */
    .pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin: 30px 0;
    }

    .pagination-controls button {
    background: #585c2c;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s ease;
    }

    .pagination-controls button:hover:not(:disabled) {
    background: #6a6f35;
    }

    .pagination-controls button:disabled {
    background: #ccc;
    cursor: not-allowed;
    }

    .pagination-info {
    font-weight: 600;
    color: #585c2c;
    }

    /* Loading and message states */
    .loading, .no-surveys, .no-results, .end-of-results {
    text-align: center;
    padding: 40px;
    color: #666;
    font-style: italic;
    }

    .end-of-results {
    padding: 20px;
    font-size: 14px;
    border-top: 1px solid #eee;
    margin-top: 20px;
    }

    /* File input styling */
    .file-input-group {
    position: relative;
    }

    .file-input-label {
    display: block;
    font-size: 12px;
    color: #666;
    margin-top: 5px;
    font-style: italic;
    }

    /* Responsive design */
    @media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    h1 {
        font-size: 2rem;
    }
    
    nav {
        flex-direction: column;
        align-items: center;
    }
    
    nav button {
        width: 200px;
    }
    
    form {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    form label {
        justify-self: start;
        margin-bottom: 5px;
    }
    
    .survey-card {
        flex-direction: column;
        gap: 20px;
    }
    
    .pagination-controls {
        flex-direction: column;
        gap: 15px;
    }
    }

    /* Enhanced focus states for accessibility */
    *:focus {
    outline: 3px solid rgba(88, 92, 44, 0.5);
    outline-offset: 2px;
    }

    /* Smooth scrolling */
    html {
    scroll-behavior: smooth;
    }

    /* Modal styles */
    .modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    }

    .modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 20px;
    border: none;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    }

    .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
    }

    .close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    }

    .close:hover,
    .close:focus {
    color: black;
    }

    .form-group {
    margin-bottom: 15px;
    }

    .form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    }

    .form-group input,
    .form-group textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    box-sizing: border-box;
    }

    .form-group textarea {
    resize: vertical;
    min-height: 60px;
    }

    .modal-buttons {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
    }

    .modal-buttons button {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    }

    .btn-primary {
    background: #007bff;
    color: white;
    }

    .btn-primary:hover {
    background: #0056b3;
    }

    .btn-secondary {
    background: #6c757d;
    color: white;
    }

    .btn-secondary:hover {
    background: #545b62;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>SkySenderos - Survey Management</h1>

    <nav>
      <button onclick="showPage('add-survey')" id="nav-add">Add Survey</button>
      <button onclick="showPage('view-surveys')" id="nav-view">View Active Surveys</button>
      <button onclick="showPage('view-archive')" id="nav-archive">View Archive</button>
    </nav>

    <!-- Add Survey Page -->
    <div id="add-survey" class="page active">
      <h2>Add New Survey</h2>
      <form id="survey-form">
        <label for="pilot">Pilot:</label>
        <input type="text" id="pilot" name="pilot" required />

        <label for="drone_name">Drone Name:</label>
        <select id="drone_name" name="drone_name" required>
          <option value="">-</option>
          <option value="SPUR">SPUR</option>
          <option value="VAQUERO">VAQUERO</option>
          <option value="SMOKEY">SMOKEY</option>
          <option value="TEX">TEX</option>
          <option value="COLT">COLT</option>
          <option value="BEVO">BEVO</option>
          <option value="PECOS">PECOS</option>
          <option value="RANGER">RANGER</option>
        </select>

        <label for="property_name">Property Name:</label>
        <input type="text" id="property_name" name="property_name" required />

        <label for="scheduled_time">Scheduled Date:</label>
        <input type="date" id="scheduled_time" name="scheduled_time" required />

        <label for="coordinates">Coordinates:</label>
        <input type="text" id="coordinates" name="coordinates" required />

        <label for="gate_code">Gate Code:</label>
        <input type="text" id="gate_code" name="gate_code" required />

        <label for="google_sheets_id">Google Sheets ID:</label>
        <input type="text" id="google_sheets_id" name="google_sheets_id" />

        <label for="notes">Notes:</label>
        <textarea id="notes" name="notes"></textarea>

        <label for="driving_instructions">Driving Instructions:</label>
        <textarea id="driving_instructions" name="driving_instructions"></textarea>

        <label for="image">Survey Image:</label>
        <div class="file-input-group">
          <input type="file" id="image" name="image" accept="image/*" required />
          <span class="file-input-label">Required - Upload survey image</span>
        </div>

        <label for="pdf">Survey PDF:</label>
        <div class="file-input-group">
          <input type="file" id="pdf" name="pdf" accept=".pdf,application/pdf" />
          <span class="file-input-label">Optional - Upload survey PDF</span>
        </div>

        <button type="submit">Add Survey</button>
      </form>
    </div>

    <!-- View Active Surveys Page -->
    <div id="view-surveys" class="page">
      <h2>Active Drone Surveys</h2>

      <div class="search-container">
        <input type="text" id="property-search" placeholder="Search by property name..." />
        <button onclick="searchSurveys()">Search</button>
        <button onclick="clearSearch()">Clear</button>
      </div>
      <div id="search-summary" style="margin: 10px 0; font-style: italic; color: #666;"></div>

      <div id="loading" class="loading" style="display: none;">Loading surveys...</div>

      <div id="surveys-container" class="survey-card-list">
        <!-- Individual survey cards will be inserted here via JavaScript -->
      </div>

      <div id="no-surveys" class="no-surveys" style="display: none;">
        No surveys found.
      </div>

      <div id="end-of-surveys" class="end-of-results" style="display: none;">
        End of surveys
      </div>

      <!-- Invisible element to trigger infinite scroll -->
      <div id="scroll-trigger" style="height: 1px; margin-bottom: 50px;"></div>
    </div>

    <!-- View Archive Page -->
    <div id="view-archive" class="page">
      <h2>Archived Surveys</h2>

      <div class="search-container">
        <input type="text" id="archive-search" placeholder="Search archived surveys by property name..." />
        <button onclick="searchArchives()">Search</button>
        <button onclick="clearArchiveSearch()">Clear</button>
      </div>
      <div id="archive-search-summary" style="margin: 10px 0; font-style: italic; color: #666;"></div>

      <div id="archive-loading" class="loading" style="display: none;">Loading archived surveys...</div>
      <div id="archive-error-message" class="error-message" style="display: none;"></div>

      <div id="archive-container"></div>

      <div id="archive-no-results" class="no-results" style="display: none;">
        No archived surveys found.
      </div>
      <div id="archive-end-of-results" class="end-of-results" style="display: none;">
        End of archived surveys
      </div>

      <!-- Invisible element to trigger infinite scroll for archives -->
      <div id="archive-scroll-trigger" style="height: 1px; margin-bottom: 50px;"></div>
    </div>

    <!-- Edit Survey Modal -->
    <div id="edit-survey-modal" class="modal" style="display: none;">
      <div class="modal-content">
        <div class="modal-header">
          <h2>Edit Survey</h2>
          <span class="close" onclick="closeEditModal()">&times;</span>
        </div>
        <form id="edit-survey-form">
          <div class="form-group">
            <label for="edit-pilot">Pilot:</label>
            <input type="text" id="edit-pilot" name="pilot" required>
          </div>
          <div class="form-group">
            <label for="edit-drone_name">Drone Name:</label>
            <select id="edit-drone_name" name="drone_name" required>
              <option value="">-</option>
              <option value="SPUR">SPUR</option>
              <option value="VAQUERO">VAQUERO</option>
              <option value="SMOKEY">SMOKEY</option>
              <option value="TEX">TEX</option>
              <option value="COLT">COLT</option>
              <option value="BEVO">BEVO</option>
              <option value="PECOS">PECOS</option>
              <option value="RANGER">RANGER</option>
            </select>
          </div>
          <div class="form-group">
            <label for="edit-property_name">Property Name:</label>
            <input type="text" id="edit-property_name" name="property_name" required>
          </div>
          <div class="form-group">
            <label for="edit-scheduled_time">Scheduled Date:</label>
            <input type="date" id="edit-scheduled_time" name="scheduled_time" required>
          </div>
          <div class="form-group">
            <label for="edit-coordinates">Coordinates:</label>
            <input type="text" id="edit-coordinates" name="coordinates" required>
          </div>
          <div class="form-group">
            <label for="edit-gate_code">Gate Code:</label>
            <input type="text" id="edit-gate_code" name="gate_code" required>
          </div>
          <div class="form-group">
            <label for="edit-google_sheets_id">Google Sheets ID:</label>
            <input type="text" id="edit-google_sheets_id" name="google_sheets_id">
          </div>
          <div class="form-group">
            <label for="edit-notes">Notes:</label>
            <textarea id="edit-notes" name="notes"></textarea>
          </div>
          <div class="form-group">
            <label for="edit-driving_instructions">Driving Instructions:</label>
            <textarea id="edit-driving_instructions" name="driving_instructions"></textarea>
          </div>
          <div class="form-group">
            <label for="edit-image">Survey Image:</label>
            <input type="file" id="edit-image" name="image" accept="image/*" />
            <span class="file-input-label">Optional - Leave blank to keep current image</span>
          </div>
          <div class="form-group">
            <label for="edit-pdf">Survey PDF:</label>
            <input type="file" id="edit-pdf" name="pdf" accept=".pdf,application/pdf" />
            <span class="file-input-label">Optional - Leave blank to keep current PDF</span>
          </div>
          <div class="modal-buttons">
            <button type="button" class="btn-secondary" onclick="closeEditModal()">Cancel</button>
            <button type="submit" class="btn-primary">Update Survey</button>
          </div>
        </form>
      </div>
    </div>

    <!-- Reinstate Survey Modal -->
    <div id="reinstateModal" class="modal" style="display: none;">
      <div class="modal-content">
        <div class="modal-header">
          <h2>Reinstate Survey</h2>
          <span class="close" onclick="closeReinstateModal()">&times;</span>
        </div>
        <form id="reinstateForm">
          <div class="form-group">
            <label for="reinstate-pilot">Pilot:</label>
            <input type="text" id="reinstate-pilot" name="pilot" required>
          </div>
          <div class="form-group">
            <label for="reinstate-drone_name">Drone Name:</label>
            <select id="reinstate-drone_name" name="drone_name" required>
              <option value="">-</option>
              <option value="SPUR">SPUR</option>
              <option value="VAQUERO">VAQUERO</option>
              <option value="SMOKEY">SMOKEY</option>
              <option value="TEX">TEX</option>
              <option value="COLT">COLT</option>
              <option value="BEVO">BEVO</option>
              <option value="PECOS">PECOS</option>
              <option value="RANGER">RANGER</option>
            </select>
          </div>
          <div class="form-group">
            <label for="reinstate-property_name">Property Name:</label>
            <input type="text" id="reinstate-property_name" name="property_name" required>
          </div>
          <div class="form-group">
            <label for="reinstate-scheduled_time">New Scheduled Date:</label>
            <input type="date" id="reinstate-scheduled_time" name="scheduled_time" required>
          </div>
          <div class="form-group">
            <label for="reinstate-coordinates">Coordinates:</label>
            <input type="text" id="reinstate-coordinates" name="coordinates" required>
          </div>
          <div class="form-group">
            <label for="reinstate-gate_code">Gate Code:</label>
            <input type="text" id="reinstate-gate_code" name="gate_code" required>
          </div>
          <div class="form-group">
            <label for="reinstate-google_sheets_id">Google Sheets ID:</label>
            <input type="text" id="reinstate-google_sheets_id" name="google_sheets_id">
          </div>
          <div class="form-group">
            <label for="reinstate-notes">Notes:</label>
            <textarea id="reinstate-notes" name="notes"></textarea>
          </div>
          <div class="form-group">
            <label for="reinstate-driving_instructions">Driving Instructions:</label>
            <textarea id="reinstate-driving_instructions" name="driving_instructions"></textarea>
          </div>
          <div class="form-group">
            <label for="reinstate-image">Survey Image:</label>
            <input type="file" id="reinstate-image" name="image" accept="image/*" />
            <span class="file-input-label">Optional - Upload new image or leave blank to keep current</span>
          </div>
          <div class="form-group">
            <label for="reinstate-pdf">Survey PDF:</label>
            <input type="file" id="reinstate-pdf" name="pdf" accept=".pdf,application/pdf" />
            <span class="file-input-label">Optional - Upload new PDF or leave blank to keep current</span>
          </div>
          <div class="modal-buttons">
            <button type="button" class="btn-secondary" onclick="closeReinstateModal()">Cancel</button>
            <button type="submit" class="btn-primary">Reinstate Survey</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <script src="skysenderos-app.js"></script>
  <script>
    const API_URL = 'https://zhtq9c7gge.execute-api.us-east-2.amazonaws.com/prod/surveys';
    const API_KEY = 'Sky193124141233';
    const PAGE_SIZE = 3;

    // State management
    let currentPage = 1;
    let isLoading = false;
    let hasMoreResults = true;
    let currentEditingSurvey = null;
    let currentSearch = '';
    let totalResults = 0;
    let surveyDataStore = {};
    let scrollObserver;

    // Archive state
    let archiveCurrentPage = 1;
    let archiveIsLoading = false;
    let archiveHasMoreResults = true;
    let archiveCurrentSearch = '';
    let archiveTotalResults = 0;
    let archiveSurveyDataStore = {};
    let currentReinstateArchiveId = null;
    let archiveScrollObserver;

    // Navigation and page management
    function showPage(pageId) {
      // Hide all pages
      document.querySelectorAll('.page').forEach(page => {
        page.classList.remove('active');
      });

      // Remove active class from all nav buttons
      document.querySelectorAll('nav button').forEach(btn => {
        btn.classList.remove('active');
      });

      // Show selected page
      document.getElementById(pageId).classList.add('active');

      // Add active class to corresponding nav button
      if (pageId === 'add-survey') {
        document.getElementById('nav-add').classList.add('active');
        clearAddForm();
      } else if (pageId === 'view-surveys') {
        document.getElementById('nav-view').classList.add('active');
        currentPage = 1;
        hasMoreResults = true;
        currentSearch = '';
        document.getElementById('property-search').value = '';
        updateSearchSummary();
        setupInfiniteScroll();
        fetchSurveys();
      } else if (pageId === 'view-archive') {
        document.getElementById('nav-archive').classList.add('active');
        archiveCurrentPage = 1;
        archiveHasMoreResults = true;
        archiveCurrentSearch = '';
        document.getElementById('archive-search').value = '';
        document.getElementById('archive-container').innerHTML = '';
        archiveSurveyDataStore = {};
        updateArchiveSearchSummary();
        setupArchiveInfiniteScroll();
        loadArchives();
      }
    }

    function clearAddForm() {
      const form = document.getElementById('survey-form');
      form.reset();

      // Clear file inputs
      const imageInput = document.getElementById('image');
      const pdfInput = document.getElementById('pdf');

      if (imageInput && !imageInput.dataset.fromDuplicate) {
        imageInput.value = '';
      }
      if (pdfInput && !pdfInput.dataset.fromDuplicate) {
        pdfInput.value = '';
      }

      // Clear duplicate flags
      if (imageInput) imageInput.dataset.fromDuplicate = '';
      if (pdfInput) pdfInput.dataset.fromDuplicate = '';
    }

    // Helper function to extract date part for input fields
    function dateToLocalDateString(dateString) {
      if (!dateString) return '';

      // Extract just the date part for date input fields
      if (dateString.includes('T')) {
        return dateString.split('T')[0];  // Handle ISO format
      }
      if (dateString.includes(' ')) {
        return dateString.split(' ')[0];  // Handle SQL timestamp format
      }

      return dateString; // Already just a date
    }

    // Helper function to convert local date input to proper datetime for API
    // Sets time to 11:59 PM Central Time on the selected date
    function localDateToDateTime(dateString) {
      if (!dateString) return null;

      // Create a simple timestamp for 11:59 PM on the selected date
      // This will be stored as Central Time in the database
      return `${dateString} 23:59:00`;
    }

    // Helper function to format dates for user-friendly display
    function formatDateForDisplay(dateString) {
      if (!dateString) return 'Not scheduled';

      // Parse the database datetime string
      const date = new Date(dateString);

      // Format as "Month Day, Year" (e.g., "September 9, 2025")
      const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      };

      return date.toLocaleDateString('en-US', options);
    }



    // File conversion helper
    function toBase64(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result.split(',')[1]);
        reader.onerror = error => reject(error);
      });
    }

    // Initialize the application
    document.addEventListener('DOMContentLoaded', function() {
      // Set default page
      showPage('add-survey');

      // Add enter key support for search
      const searchInput = document.getElementById('property-search');
      if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
          if (e.key === 'Enter') {
            searchSurveys();
          }
        });
      }

      const archiveSearchInput = document.getElementById('archive-search');
      if (archiveSearchInput) {
        archiveSearchInput.addEventListener('keypress', function(e) {
          if (e.key === 'Enter') {
            searchArchives();
          }
        });
      }
    });

    // Search functionality for active surveys
    function searchSurveys() {
      const searchValue = document.getElementById('property-search').value.trim();
      console.log('Searching for:', searchValue);

      currentSearch = searchValue;
      currentPage = 1;
      hasMoreResults = true;
      totalResults = 0;

      // Clear existing results
      document.getElementById('surveys-container').innerHTML = '';
      surveyDataStore = {};

      updateSearchSummary();
      hideMessages();
      fetchSurveys();
    }

    function clearSearch() {
      document.getElementById('property-search').value = '';
      currentSearch = '';
      currentPage = 1;
      hasMoreResults = true;
      totalResults = 0;

      // Clear existing results
      document.getElementById('surveys-container').innerHTML = '';
      surveyDataStore = {};

      updateSearchSummary();
      hideMessages();
      fetchSurveys();
    }

    function updateSearchSummary() {
      const summaryElement = document.getElementById('search-summary');
      if (currentSearch) {
        summaryElement.textContent = `Searching for: "${currentSearch}"`;
        summaryElement.style.display = 'block';
      } else {
        summaryElement.textContent = '';
        summaryElement.style.display = 'none';
      }
    }

    function hideMessages() {
      document.getElementById('no-surveys').style.display = 'none';
      document.getElementById('end-of-surveys').style.display = 'none';
    }

    // Infinite scroll setup
    function setupInfiniteScroll() {
      // Disconnect existing observer if any
      if (scrollObserver) {
        scrollObserver.disconnect();
      }

      const scrollTrigger = document.getElementById('scroll-trigger');
      if (!scrollTrigger) return;

      scrollObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          console.log(`Scroll trigger intersecting: ${entry.isIntersecting}, hasMoreResults: ${hasMoreResults}, isLoading: ${isLoading}`);
          if (entry.isIntersecting && hasMoreResults && !isLoading) {
            console.log('Triggering infinite scroll - loading more surveys...');
            fetchSurveys();
          }
        });
      }, {
        rootMargin: '100px' // Start loading 100px before the trigger comes into view
      });

      scrollObserver.observe(scrollTrigger);
    }

    // Archive infinite scroll setup
    function setupArchiveInfiniteScroll() {
      // Disconnect existing observer if any
      if (archiveScrollObserver) {
        archiveScrollObserver.disconnect();
      }

      const archiveScrollTrigger = document.getElementById('archive-scroll-trigger');
      if (!archiveScrollTrigger) return;

      archiveScrollObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          console.log(`Archive scroll trigger intersecting: ${entry.isIntersecting}, archiveHasMoreResults: ${archiveHasMoreResults}, archiveIsLoading: ${archiveIsLoading}`);
          if (entry.isIntersecting && archiveHasMoreResults && !archiveIsLoading) {
            console.log('Triggering archive infinite scroll - loading more archived surveys...');
            loadArchives();
          }
        });
      }, {
        rootMargin: '100px' // Start loading 100px before the trigger comes into view
      });

      archiveScrollObserver.observe(archiveScrollTrigger);
    }

    function setLoadingState(loading) {
      isLoading = loading;
      const loadingElement = document.getElementById('loading');

      if (loading) {
        loadingElement.style.display = 'block';
      } else {
        loadingElement.style.display = 'none';
      }
    }

    // Fetch active surveys with infinite scroll
    async function fetchSurveys() {
      if (isLoading || !hasMoreResults) return;

      console.log("fetching surveys for page", currentPage);
      setLoadingState(true);
      hideMessages();

      try {
        const pilot = 'Master';
        const droneName = 'Master';
        const datetime = new Date().toISOString();

        const url = new URL(API_URL);
        url.searchParams.append('pilot', pilot);
        url.searchParams.append('drone_name', droneName);
        url.searchParams.append('datetime', datetime);
        url.searchParams.append('page', currentPage.toString());

        if (currentSearch) {
          url.searchParams.append('property_name', currentSearch);
        }

        console.log("Fetching URL:", url.toString());

        const res = await fetch(url.toString(), {
          method: 'GET',
          headers: {
            'x-api-key': API_KEY
          }
        });

        console.log("Response status:", res.status);
        console.log("Response ok:", res.ok);

        if (!res.ok) {
          throw new Error(`HTTP error! status: ${res.status}`);
        }

        const surveys = await res.json();
        console.log("got response", surveys);

        const surveyData = surveys.data || [];

        if (surveyData.length === 0) {
          hasMoreResults = false;
          if (currentPage === 1) {
            document.getElementById('no-surveys').style.display = 'block';
          } else {
            document.getElementById('end-of-surveys').style.display = 'block';
          }
        } else {
          console.log(`Got ${surveyData.length} surveys for page ${currentPage}, PAGE_SIZE is ${PAGE_SIZE}`);

          // Append new surveys to existing ones (don't clear on page > 1)
          renderSurveys(surveyData, currentPage > 1);
          currentPage++;
          totalResults += surveyData.length;

          console.log(`Total results so far: ${totalResults}, hasMoreResults: ${hasMoreResults}`);

          // Only show end message if we got fewer results than PAGE_SIZE
          if (surveyData.length < PAGE_SIZE) {
            hasMoreResults = false;
            console.log('Setting hasMoreResults to false - got fewer results than PAGE_SIZE');
            // Only show end message if we have loaded some results
            if (totalResults > 0) {
              document.getElementById('end-of-surveys').style.display = 'block';
            }
          } else {
            console.log('Still have more results - got full PAGE_SIZE');
          }
        }

      } catch (err) {
        console.error("error fetching", err);
        console.error("Error details:", err.message);
        alert(`Error fetching surveys: ${err.message}`);
      } finally {
        setLoadingState(false);
      }
    }

    // Make functions and state globally accessible for external JS file
    window.hideMessages = hideMessages;
    window.fetchSurveys = fetchSurveys;
    window.surveyState = {
      get currentPage() { return currentPage; },
      set currentPage(value) { currentPage = value; },
      get hasMoreResults() { return hasMoreResults; },
      set hasMoreResults(value) { hasMoreResults = value; },
      get totalResults() { return totalResults; },
      set totalResults(value) { totalResults = value; },
      get surveyDataStore() { return surveyDataStore; },
      set surveyDataStore(value) { surveyDataStore = value; }
    };

    // Form submission handlers
    document.getElementById('survey-form').addEventListener('submit', async (e) => {
      e.preventDefault();
      const formData = new FormData(e.target);

      try {
        const imageFile = formData.get('image');
        const pdfFile = formData.get('pdf');

        console.log('Image file:', imageFile);
        console.log('PDF file:', pdfFile);

        if (!imageFile || !imageFile.type.startsWith('image/')) {
          throw new Error("Please upload a valid image file.");
        }

        const base64Image = await toBase64(imageFile);
        let base64Pdf = null;

        // Handle PDF upload if provided
        if (pdfFile && pdfFile.size > 0) {
          if (pdfFile.type !== 'application/pdf') {
            throw new Error("Please upload a valid PDF file.");
          }
          base64Pdf = await toBase64(pdfFile);
        }

        const dateOnly = formData.get('scheduled_time');
        const isoUtc = localDateToDateTime(dateOnly);

        const data = {
            pilot: formData.get('pilot'),
            drone_name: formData.get('drone_name'),
            scheduled_time: isoUtc,
            coordinates: formData.get('coordinates'),
            gate_code: formData.get('gate_code'),
            notes: formData.get('notes'),
            driving_instructions: formData.get('driving_instructions'),
            image_base64: base64Image,
            google_sheets_id: formData.get('google_sheets_id'),
            property_name: formData.get('property_name')
        };

        // Only add PDF if provided
        if (base64Pdf) {
          data.pdf_base64 = base64Pdf;
        }

        console.log('Sending data:', data);

        const res = await fetch(API_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': API_KEY
          },
          body: JSON.stringify(data)
        });

        console.log('Response status:', res.status);

        if (!res.ok) {
          const errorData = await res.json();
          throw new Error(errorData.error || `HTTP error! status: ${res.status}`);
        }

        const result = await res.json();
        console.log('Success:', result);

        alert('Survey added successfully!');
        e.target.reset();

        // Clear file input flags
        const imageInput = document.getElementById('image');
        const pdfInput = document.getElementById('pdf');
        if (imageInput) imageInput.dataset.fromDuplicate = '';
        if (pdfInput) pdfInput.dataset.fromDuplicate = '';

      } catch (err) {
        console.error('Error:', err);
        alert(`Error: ${err.message}`);
      }
    });

    // Edit form submission handler
    document.getElementById('edit-survey-form').addEventListener('submit', async (e) => {
      e.preventDefault();
      const formData = new FormData(e.target);

      try {
        if (!currentEditingSurvey || !currentEditingSurvey.id) {
          throw new Error("No survey selected for editing.");
        }

        const dateOnly = formData.get('scheduled_time');
        const isoUtc = localDateToDateTime(dateOnly);

        const data = {
            id: currentEditingSurvey.id,
            pilot: formData.get('pilot'),
            drone_name: formData.get('drone_name'),
            scheduled_time: isoUtc,
            coordinates: formData.get('coordinates'),
            gate_code: formData.get('gate_code'),
            notes: formData.get('notes'),
            driving_instructions: formData.get('driving_instructions'),
            google_sheets_id: formData.get('google_sheets_id'),
            property_name: formData.get('property_name')
        };

        // Only include image if a new one was uploaded
        const imageFile = formData.get('image');
        if (imageFile && imageFile.size > 0) {
          if (!imageFile.type.startsWith('image/')) {
            throw new Error("Please upload a valid image file.");
          }
          const base64Image = await toBase64(imageFile);
          data.image_base64 = base64Image;
        }

        // Only include PDF if a new one was uploaded
        const pdfFile = formData.get('pdf');
        if (pdfFile && pdfFile.size > 0) {
          if (pdfFile.type !== 'application/pdf') {
            throw new Error("Please upload a valid PDF file.");
          }
          const base64Pdf = await toBase64(pdfFile);
          data.pdf_base64 = base64Pdf;
        }

        console.log('Sending edit data:', data);

        const res = await fetch(API_URL, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': API_KEY
          },
          body: JSON.stringify(data)
        });

        if (!res.ok) {
          const errorData = await res.json();
          throw new Error(errorData.error || `HTTP error! status: ${res.status}`);
        }

        const result = await res.json();
        console.log('Edit success:', result);

        alert('Survey updated successfully!');
        closeEditModal();

        // Refresh the surveys list by resetting pagination and reloading
        currentPage = 1;
        hasMoreResults = true;
        totalResults = 0;
        document.getElementById('surveys-container').innerHTML = '';
        surveyDataStore = {};
        hideMessages();
        fetchSurveys();

      } catch (err) {
        console.error('Edit error:', err);
        alert(`Error: ${err.message}`);
      }
    });

    // Modal click outside to close
    window.onclick = function(event) {
      const editModal = document.getElementById('edit-survey-modal');
      const reinstateModal = document.getElementById('reinstateModal');

      if (event.target === editModal) {
        closeEditModal();
      }
      if (event.target === reinstateModal) {
        closeReinstateModal();
      }
    }
  </script>
</body>
</html>
