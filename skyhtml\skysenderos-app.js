// Survey rendering and management functions

function renderSurveys(surveys, append = false) {
  const container = document.getElementById('surveys-container');

  if (!append) {
    container.innerHTML = '';
    surveyDataStore = {}; // Clear previous data
  }

  if (surveys.length === 0) {
    if (!append) {
      document.getElementById('no-surveys').style.display = 'block';
      container.style.display = 'none';
    }
    return;
  }

  document.getElementById('no-surveys').style.display = 'none';
  container.style.display = 'block';

  for (let i = 0; i < surveys.length; i++) {
    const survey = surveys[i];
    console.log('Rendering survey:', survey);

    const card = document.createElement('div');
    card.className = 'survey-card';

    // Generate unique ID for this survey using actual survey ID if available
    const surveyId = `survey_${survey.id || `${currentPage}_${i}`}`;
    surveyDataStore[surveyId] = survey;

    // Process image data and create embedded viewer
    let imageHtml = '';
    if (survey.image) {
      console.log("Survey has image data:", typeof survey.image);
      
      const imageSrc = processImageData(survey.image);
      
      if (imageSrc) {
        imageHtml = `
          <div class="survey-image">
            <img src="${imageSrc}" alt="Survey Image" style="max-width: 300px; max-height: 200px; border-radius: 8px;" />
          </div>
        `;
      } else {
        imageHtml = `
          <div class="image-error">
            Unable to process image data
          </div>
        `;
      }
    }

    // Process PDF data and create embedded viewer
    let pdfHtml = '';
    if (survey.pdf) {
      console.log("Survey has PDF data:", typeof survey.pdf);
      
      const pdfSrc = processPdfData(survey.pdf);
      
      if (pdfSrc) {
        const pdfId = `pdf-${surveyId}`;
        pdfHtml = `
          <div class="pdf-section">
            <div class="pdf-viewer" id="${pdfId}-viewer">
              <iframe src="${pdfSrc}#toolbar=0&navpanes=0&scrollbar=0" 
                      title="Survey PDF" 
                      style="width: 300px; height: 200px; border: none; border-radius: 8px;"
                      onerror="showPdfError('${pdfId}')">
              </iframe>
            </div>
            <div class="pdf-controls" style="margin-top: 10px; text-align: center;">
              <a href="${pdfSrc}" class="pdf-link" download="survey-${survey.property_name || 'document'}.pdf" 
                 style="background: #dc3545; color: white; padding: 5px 10px; text-decoration: none; border-radius: 4px; font-size: 12px;">
                Download PDF
              </a>
            </div>
          </div>
        `;
      } else {
        pdfHtml = `
          <div class="pdf-section">
            <div class="pdf-error" style="padding: 20px; text-align: center; color: #666; border: 1px dashed #ccc; border-radius: 8px;">
              <div>Unable to process PDF data</div>
            </div>
          </div>
        `;
      }
    }

    // Combine attachments
    let attachmentsHtml = '';
    if (imageHtml || pdfHtml) {
      attachmentsHtml = `
        <div class="survey-attachments" style="flex: 0 0 30%; display: flex; flex-direction: column; gap: 15px; align-items: center; width: 300px;">
          ${imageHtml}
          ${pdfHtml}
        </div>
      `;
    }

    card.innerHTML = `
        <div class="survey-info">
            <p><strong>Property Name:</strong> ${survey.property_name || ''}</p>
            <p><strong>Pilot:</strong> ${survey.pilot || ''}</p>
            <p><strong>Drone:</strong> ${survey.drone_name || ''}</p>
            <p><strong>Scheduled Time:</strong> ${formatDateForDisplay(survey.scheduled_time)}</p>
            <p><strong>Coordinates:</strong> ${survey.coordinates || ''}</p>
            <p><strong>Gate Code:</strong> ${survey.gate_code || ''}</p>
            <p><strong>Notes:</strong> ${survey.notes || ''}</p>
            <p><strong>Driving Instructions:</strong> ${survey.driving_instructions || ''}</p>
            <p><strong>Google Sheets ID:</strong> ${survey.google_sheets_id || ''}</p>
            <button class="edit-btn" onclick="editSurveyById('${surveyId}')">Edit</button>
            <button class="duplicate-btn" onclick="duplicateSurveyById('${surveyId}')">Duplicate</button>
            <button onclick="deleteSurveyById('${surveyId}')">Delete</button>
            <p><strong>ID:</strong> ${survey.id || ''}</p>
      </div>
      ${attachmentsHtml}
    `;

    container.appendChild(card);
  }
}

// Image and PDF processing functions
function processImageData(imageData) {
  console.log("Processing image data:", typeof imageData, imageData);

  // If it's null or undefined, return null
  if (!imageData) {
    console.log("No image data provided");
    return null;
  }

  // If it's already a data URL string, return as is
  if (typeof imageData === 'string' && imageData.startsWith('data:')) {
    console.log("Image is already a data URL");
    return imageData;
  }

  // If it's a base64 string without data URL prefix
  if (typeof imageData === 'string' && !imageData.startsWith('data:')) {
    console.log("Image is base64 string, adding data URL prefix");
    return `data:image/jpeg;base64,${imageData}`;
  }

  // Handle PostgreSQL Buffer objects (they have type: 'Buffer' and data array)
  if (typeof imageData === 'object' && imageData.type === 'Buffer' && Array.isArray(imageData.data)) {
    console.log("Processing PostgreSQL Buffer object");
    try {
      const uint8Array = new Uint8Array(imageData.data);
      const base64 = uint8ArrayToBase64(uint8Array);

      // Detect image type from first few bytes
      const isPNG = uint8Array[0] === 0x89 && uint8Array[1] === 0x50 && uint8Array[2] === 0x4E && uint8Array[3] === 0x47;
      const isJPEG = uint8Array[0] === 0xFF && uint8Array[1] === 0xD8;

      let mimeType = 'image/png'; // default to PNG
      if (isJPEG) {
        mimeType = 'image/jpeg';
      } else if (isPNG) {
        mimeType = 'image/png';
      }

      return `data:${mimeType};base64,${base64}`;
    } catch (error) {
      console.error("Error processing PostgreSQL Buffer:", error);
      return null;
    }
  }

  // Handle ArrayBuffer
  if (imageData instanceof ArrayBuffer) {
    console.log("Processing ArrayBuffer");
    try {
      const uint8Array = new Uint8Array(imageData);
      const base64 = uint8ArrayToBase64(uint8Array);

      // Detect image type from first few bytes
      const isPNG = uint8Array[0] === 0x89 && uint8Array[1] === 0x50 && uint8Array[2] === 0x4E && uint8Array[3] === 0x47;
      const isJPEG = uint8Array[0] === 0xFF && uint8Array[1] === 0xD8;

      let mimeType = 'image/png'; // default to PNG
      if (isJPEG) {
        mimeType = 'image/jpeg';
      } else if (isPNG) {
        mimeType = 'image/png';
      }

      return `data:${mimeType};base64,${base64}`;
    } catch (error) {
      console.error("Error processing ArrayBuffer:", error);
      return null;
    }
  }

  // Handle Uint8Array
  if (imageData instanceof Uint8Array) {
    console.log("Processing Uint8Array");
    try {
      const base64 = uint8ArrayToBase64(imageData);

      // Detect image type from first few bytes
      const isPNG = imageData[0] === 0x89 && imageData[1] === 0x50 && imageData[2] === 0x4E && imageData[3] === 0x47;
      const isJPEG = imageData[0] === 0xFF && imageData[1] === 0xD8;

      let mimeType = 'image/png'; // default to PNG
      if (isJPEG) {
        mimeType = 'image/jpeg';
      } else if (isPNG) {
        mimeType = 'image/png';
      }

      return `data:${mimeType};base64,${base64}`;
    } catch (error) {
      console.error("Error processing Uint8Array:", error);
      return null;
    }
  }

  // Handle regular arrays (assume they're byte arrays)
  if (Array.isArray(imageData)) {
    console.log("Processing array as byte array");
    try {
      const uint8Array = new Uint8Array(imageData);
      const base64 = uint8ArrayToBase64(uint8Array);

      // Detect image type from first few bytes
      const isPNG = uint8Array[0] === 0x89 && uint8Array[1] === 0x50 && uint8Array[2] === 0x4E && uint8Array[3] === 0x47;
      const isJPEG = uint8Array[0] === 0xFF && uint8Array[1] === 0xD8;

      let mimeType = 'image/png'; // default to PNG
      if (isJPEG) {
        mimeType = 'image/jpeg';
      } else if (isPNG) {
        mimeType = 'image/png';
      }

      return `data:${mimeType};base64,${base64}`;
    } catch (error) {
      console.error("Error processing array:", error);
      return null;
    }
  }

  // Handle generic objects that might have a data property
  if (typeof imageData === 'object' && imageData.data) {
    console.log("Processing object with data property");
    return processImageData(imageData.data);
  }

  console.warn("Unknown image data format:", typeof imageData, imageData);
  return null;
}

function processPdfData(pdfData) {
  console.log("Processing PDF data:", typeof pdfData, pdfData);

  // If it's null or undefined, return null
  if (!pdfData) {
    console.log("No PDF data provided");
    return null;
  }

  // If it's already a data URL string, return as is
  if (typeof pdfData === 'string' && pdfData.startsWith('data:')) {
    console.log("PDF is already a data URL");
    return pdfData;
  }

  // If it's a base64 string without data URL prefix
  if (typeof pdfData === 'string' && !pdfData.startsWith('data:')) {
    console.log("PDF is base64 string, adding data URL prefix");
    return `data:application/pdf;base64,${pdfData}`;
  }

  // Handle PostgreSQL Buffer objects (they have type: 'Buffer' and data array)
  if (typeof pdfData === 'object' && pdfData.type === 'Buffer' && Array.isArray(pdfData.data)) {
    console.log("Processing PostgreSQL PDF Buffer object");
    try {
      const uint8Array = new Uint8Array(pdfData.data);
      const base64 = uint8ArrayToBase64(uint8Array);
      return `data:application/pdf;base64,${base64}`;
    } catch (error) {
      console.error("Error processing PostgreSQL PDF Buffer:", error);
      return null;
    }
  }

  // Handle ArrayBuffer
  if (pdfData instanceof ArrayBuffer) {
    console.log("Processing PDF ArrayBuffer");
    try {
      const uint8Array = new Uint8Array(pdfData);
      const base64 = uint8ArrayToBase64(uint8Array);
      return `data:application/pdf;base64,${base64}`;
    } catch (error) {
      console.error("Error processing PDF ArrayBuffer:", error);
      return null;
    }
  }

  // Handle Uint8Array
  if (pdfData instanceof Uint8Array) {
    console.log("Processing PDF Uint8Array");
    try {
      const base64 = uint8ArrayToBase64(uint8Array);
      return `data:application/pdf;base64,${base64}`;
    } catch (error) {
      console.error("Error processing PDF Uint8Array:", error);
      return null;
    }
  }

  // Handle regular arrays (assume they're byte arrays)
  if (Array.isArray(pdfData)) {
    console.log("Processing PDF array as byte array");
    try {
      const uint8Array = new Uint8Array(pdfData);
      const base64 = uint8ArrayToBase64(uint8Array);
      return `data:application/pdf;base64,${base64}`;
    } catch (error) {
      console.error("Error processing PDF array:", error);
      return null;
    }
  }

  // Handle generic objects that might have a data property
  if (typeof pdfData === 'object' && pdfData.data) {
    console.log("Processing PDF object with data property");
    return processPdfData(pdfData.data);
  }

  console.warn("Unknown PDF data format:", typeof pdfData, pdfData);
  return null;
}

function uint8ArrayToBase64(uint8Array) {
  let result = '';
  const chunkSize = 8192;
  
  for (let i = 0; i < uint8Array.length; i += chunkSize) {
    const chunk = uint8Array.slice(i, i + chunkSize);
    result += String.fromCharCode.apply(null, chunk);
  }
  
  return btoa(result);
}

// Survey management functions
async function deleteSurveyById(surveyId) {
  const survey = surveyDataStore[surveyId];
  if (!survey) {
    alert('Error: Survey not found');
    return;
  }

  const confirmDelete = confirm(`Are you sure you want to archive the survey "${survey.property_name}" (ID: ${survey.id})?`);
  if (!confirmDelete) return;

  try {
    const url = new URL(API_URL);
    url.searchParams.append('id', survey.id.toString());

    const res = await fetch(url.toString(), {
      method: 'DELETE',
      headers: {
        'x-api-key': API_KEY
      }
    });

    if (!res.ok) {
      throw new Error(`Failed to archive survey (status ${res.status})`);
    }

    alert('Survey archived successfully');

    // Refresh the entire survey list to ensure consistency
    if (window.surveyState) {
      window.surveyState.currentPage = 1;
      window.surveyState.hasMoreResults = true;
      window.surveyState.totalResults = 0;
      window.surveyState.surveyDataStore = {};
    }

    document.getElementById('surveys-container').innerHTML = '';

    // Hide any messages and reload
    if (window.hideMessages) {
      window.hideMessages();
    }

    // Fetch fresh data
    if (window.fetchSurveys) {
      window.fetchSurveys();
    }

  } catch (err) {
    alert(`Error archiving survey: ${err.message}`);
  }
}

async function editSurveyById(surveyId) {
  const survey = surveyDataStore[surveyId];
  if (survey) {
    editSurvey(survey);
  } else {
    console.error('Survey not found:', surveyId);
  }
}

async function duplicateSurveyById(surveyId) {
  const survey = surveyDataStore[surveyId];
  if (survey) {
    await duplicateSurvey(survey);
  } else {
    console.error('Survey not found:', surveyId);
  }
}

function editSurvey(survey) {
  console.log("Editing survey:", survey);
  currentEditingSurvey = survey;

  // Populate form fields
  document.getElementById('edit-pilot').value = survey.pilot || '';
  document.getElementById('edit-drone_name').value = survey.drone_name || '';
  document.getElementById('edit-property_name').value = survey.property_name || '';
  document.getElementById('edit-coordinates').value = survey.coordinates || '';
  document.getElementById('edit-gate_code').value = survey.gate_code || '';
  document.getElementById('edit-google_sheets_id').value = survey.google_sheets_id || '';
  document.getElementById('edit-notes').value = survey.notes || '';
  document.getElementById('edit-driving_instructions').value = survey.driving_instructions || '';

  // Convert scheduled_time to date format for input (preserving local date)
  if (survey.scheduled_time) {
    const dateString = dateToLocalDateString(survey.scheduled_time);
    document.getElementById('edit-scheduled_time').value = dateString;
  }

  // Clear file inputs to prevent auto-upload of previous files
  const editImageInput = document.getElementById('edit-image');
  const editPdfInput = document.getElementById('edit-pdf');

  if (editImageInput) {
    editImageInput.value = '';
  }
  if (editPdfInput) {
    editPdfInput.value = '';
  }

  // Show edit modal
  document.getElementById('edit-survey-modal').style.display = 'block';
}

function closeEditModal() {
  document.getElementById('edit-survey-modal').style.display = 'none';
  currentEditingSurvey = null;
}

async function duplicateSurvey(survey) {
  console.log("Duplicating survey:", survey);

  // Switch to add survey page
  showPage('add-survey');

  // Populate add form fields with survey data
  document.getElementById('pilot').value = survey.pilot || '';
  document.getElementById('drone_name').value = survey.drone_name || '';
  document.getElementById('property_name').value = survey.property_name || '';
  document.getElementById('coordinates').value = survey.coordinates || '';
  document.getElementById('gate_code').value = survey.gate_code || '';
  document.getElementById('google_sheets_id').value = survey.google_sheets_id || '';
  document.getElementById('notes').value = survey.notes || '';
  document.getElementById('driving_instructions').value = survey.driving_instructions || '';

  // Convert scheduled_time to date format for input (preserving local date)
  if (survey.scheduled_time) {
    const dateString = dateToLocalDateString(survey.scheduled_time);
    document.getElementById('scheduled_time').value = dateString;
  }

  let imageSuccess = false;
  let pdfSuccess = false;
  let errors = [];

  // Copy image file if it exists
  if (survey.image) {
    try {
      console.log("Processing image for duplication...");
      const imageDataUrl = processImageData(survey.image);
      console.log("Image data URL created:", imageDataUrl ? "Success" : "Failed");

      if (imageDataUrl) {
        const imageFile = await dataURLToFile(imageDataUrl, `duplicated-image-${survey.property_name || 'survey'}.jpg`);
        console.log("Image file created:", imageFile.name, imageFile.size, "bytes");

        const imageInput = document.getElementById('image');

        // Create a new FileList with our file
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(imageFile);
        imageInput.files = dataTransfer.files;

        // Mark as coming from duplicate to prevent clearing
        imageInput.dataset.fromDuplicate = 'true';

        console.log("Image file assigned to input");
        imageSuccess = true;
      } else {
        errors.push("Could not process image data");
      }
    } catch (error) {
      console.error("Error duplicating image:", error);
      errors.push(`Image duplication failed: ${error.message}`);
    }
  }

  // Copy PDF file if it exists
  if (survey.pdf) {
    try {
      console.log("Processing PDF for duplication...");
      const pdfDataUrl = processPdfData(survey.pdf);
      console.log("PDF data URL created:", pdfDataUrl ? "Success" : "Failed");

      if (pdfDataUrl) {
        const pdfFile = await dataURLToFile(pdfDataUrl, `duplicated-pdf-${survey.property_name || 'survey'}.pdf`);
        console.log("PDF file created:", pdfFile.name, pdfFile.size, "bytes");

        const pdfInput = document.getElementById('pdf');

        // Create a new FileList with our file
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(pdfFile);
        pdfInput.files = dataTransfer.files;

        // Mark as coming from duplicate to prevent clearing
        pdfInput.dataset.fromDuplicate = 'true';

        console.log("PDF file assigned to input");
        pdfSuccess = true;
      } else {
        errors.push("Could not process PDF data");
      }
    } catch (error) {
      console.error("Error duplicating PDF:", error);
      errors.push(`PDF duplication failed: ${error.message}`);
    }
  }

  // Show status message
  let message = "Survey data duplicated successfully!";
  if (imageSuccess && pdfSuccess) {
    message += " Both image and PDF files have been copied.";
  } else if (imageSuccess) {
    message += " Image file has been copied.";
    if (errors.length > 0) {
      message += ` Note: ${errors.join(', ')}`;
    }
  } else if (pdfSuccess) {
    message += " PDF file has been copied.";
    if (errors.length > 0) {
      message += ` Note: ${errors.join(', ')}`;
    }
  } else if (errors.length > 0) {
    message = `Survey data duplicated, but there were issues with files: ${errors.join(', ')}`;
  }

  alert(message);
}

// Utility function to convert data URL to File object
async function dataURLToFile(dataURL, filename) {
  const response = await fetch(dataURL);
  const blob = await response.blob();
  return new File([blob], filename, { type: blob.type });
}

// Archive functionality
function searchArchives() {
  const searchValue = document.getElementById('archive-search').value.trim();
  console.log('Searching archives for:', searchValue);

  archiveCurrentSearch = searchValue;
  archiveCurrentPage = 1;
  archiveHasMoreResults = true;
  archiveTotalResults = 0;

  // Clear existing results
  document.getElementById('archive-container').innerHTML = '';
  archiveSurveyDataStore = {};

  // Update UI
  updateArchiveSearchSummary();
  hideArchiveMessages();

  // Setup infinite scroll and load first page
  setupArchiveInfiniteScroll();
  loadArchives();
}

function clearArchiveSearch() {
  document.getElementById('archive-search').value = '';
  archiveCurrentSearch = '';
  archiveCurrentPage = 1;
  archiveHasMoreResults = true;
  archiveTotalResults = 0;

  // Clear existing results
  document.getElementById('archive-container').innerHTML = '';
  archiveSurveyDataStore = {};

  // Update UI
  updateArchiveSearchSummary();
  hideArchiveMessages();

  // Setup infinite scroll and load first page
  setupArchiveInfiniteScroll();
  loadArchives();
}

function updateArchiveSearchSummary() {
  const summaryElement = document.getElementById('archive-search-summary');
  if (archiveCurrentSearch) {
    summaryElement.textContent = `Searching for: "${archiveCurrentSearch}"`;
    summaryElement.style.display = 'block';
  } else {
    summaryElement.textContent = '';
    summaryElement.style.display = 'none';
  }
}

function hideArchiveMessages() {
  document.getElementById('archive-no-results').style.display = 'none';
  document.getElementById('archive-end-of-results').style.display = 'none';
  document.getElementById('archive-error-message').style.display = 'none';
}

function setArchiveLoadingState(loading) {
  archiveIsLoading = loading;
  const loadingElement = document.getElementById('archive-loading');

  if (loading) {
    loadingElement.style.display = 'block';
  } else {
    loadingElement.style.display = 'none';
  }
}

async function loadArchives() {
  if (archiveIsLoading || !archiveHasMoreResults) return;

  console.log("Loading archives for page", archiveCurrentPage);
  setArchiveLoadingState(true);
  hideArchiveMessages();

  try {
    const url = new URL(API_URL);
    url.searchParams.append('archives', 'true');
    url.searchParams.append('page', archiveCurrentPage.toString());
    // Required parameters to satisfy the existing API validation
    url.searchParams.append('pilot', 'Master');
    url.searchParams.append('drone_name', 'Master');
    url.searchParams.append('datetime', new Date().toISOString());

    if (archiveCurrentSearch) {
      url.searchParams.append('property_name', archiveCurrentSearch);
    }

    console.log('Fetching archive URL:', url.toString());

    const res = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'x-api-key': API_KEY
      }
    });

    console.log('Archive response status:', res.status);

    if (!res.ok) {
      throw new Error(`HTTP error! status: ${res.status}`);
    }

    const result = await res.json();
    console.log('Archive response:', result);

    const surveys = result.data || [];

    if (surveys.length === 0) {
      archiveHasMoreResults = false;
      if (archiveCurrentPage === 1) {
        document.getElementById('archive-no-results').style.display = 'block';
      } else {
        document.getElementById('archive-end-of-results').style.display = 'block';
      }
    } else {
      console.log(`Got ${surveys.length} archived surveys for page ${archiveCurrentPage}, PAGE_SIZE is 3`);

      renderArchiveSurveys(surveys);
      archiveCurrentPage++;
      archiveTotalResults += surveys.length;

      console.log(`Total archive results so far: ${archiveTotalResults}, archiveHasMoreResults: ${archiveHasMoreResults}`);

      // Only show end message if we got fewer results than PAGE_SIZE (3)
      if (surveys.length < 3) {
        archiveHasMoreResults = false;
        console.log('Setting archiveHasMoreResults to false - got fewer results than PAGE_SIZE');
        // Only show end message if we have loaded some results
        if (archiveTotalResults > 0) {
          document.getElementById('archive-end-of-results').style.display = 'block';
        }
      } else {
        console.log('Still have more archive results - got full PAGE_SIZE');
      }
    }

  } catch (err) {
    console.error("Error loading archives:", err);
    const errorElement = document.getElementById('archive-error-message');
    errorElement.textContent = `Error loading archived surveys: ${err.message}`;
    errorElement.style.display = 'block';
  } finally {
    setArchiveLoadingState(false);
  }
}

function renderArchiveSurveys(surveys) {
  const container = document.getElementById('archive-container');

  for (let i = 0; i < surveys.length; i++) {
    const survey = surveys[i];
    console.log('Rendering archived survey:', survey);

    const card = document.createElement('div');
    card.className = 'survey-card';

    // Generate unique ID for this survey
    const surveyId = `archived_survey_${archiveCurrentPage}_${i}`;
    archiveSurveyDataStore[surveyId] = survey;

    // Process image data and create embedded viewer
    let imageHtml = '';
    if (survey.image) {
      const imageSrc = processImageData(survey.image);

      if (imageSrc) {
        imageHtml = `
          <div class="survey-image">
            <img src="${imageSrc}" alt="Survey Image" style="max-width: 300px; max-height: 200px; border-radius: 8px;" />
          </div>
        `;
      } else {
        imageHtml = `
          <div class="image-error">
            Unable to process image data
          </div>
        `;
      }
    }

    // Process PDF data and create embedded viewer
    let pdfHtml = '';
    if (survey.pdf) {
      const pdfSrc = processPdfData(survey.pdf);

      if (pdfSrc) {
        const pdfId = `pdf-${surveyId}`;
        pdfHtml = `
          <div class="pdf-section">
            <div class="pdf-viewer" id="${pdfId}-viewer">
              <iframe src="${pdfSrc}#toolbar=0&navpanes=0&scrollbar=0"
                      title="Survey PDF"
                      style="width: 300px; height: 200px; border: none; border-radius: 8px;"
                      onerror="showPdfError('${pdfId}')">
              </iframe>
            </div>
          </div>
        `;
      } else {
        pdfHtml = `
          <div class="pdf-section">
            <div class="pdf-error" style="padding: 20px; text-align: center; color: #666; border: 1px dashed #ccc; border-radius: 8px;">
              <div>Unable to process PDF data</div>
            </div>
          </div>
        `;
      }
    }

    // Combine attachments
    let attachmentsHtml = '';
    if (imageHtml || pdfHtml) {
      attachmentsHtml = `
        <div class="survey-attachments" style="flex: 0 0 30%; display: flex; flex-direction: column; gap: 15px; align-items: center; width: 300px;">
          ${imageHtml}
          ${pdfHtml}
        </div>
      `;
    }

    // Format archived date
    const archivedDate = new Date(survey.archived_at).toLocaleString();
    const originalDate = formatDateForDisplay(survey.scheduled_time);

    card.innerHTML = `
      <div class="survey-info">
        <p><strong>Property Name:</strong> ${survey.property_name || ''}<span class="archived-badge">ARCHIVED</span></p>
        <p><strong>Pilot:</strong> ${survey.pilot || ''}</p>
        <p><strong>Drone:</strong> ${survey.drone_name || ''}</p>
        <p><strong>Original Scheduled Time:</strong> ${originalDate}</p>
        <p><strong>Archived On:</strong> ${archivedDate}</p>
        <p><strong>Coordinates:</strong> ${survey.coordinates || ''}</p>
        <p><strong>Gate Code:</strong> ${survey.gate_code || ''}</p>
        <p><strong>Notes:</strong> ${survey.notes || ''}</p>
        <p><strong>Driving Instructions:</strong> ${survey.driving_instructions || ''}</p>
        <p><strong>Google Sheets ID:</strong> ${survey.google_sheets_id || ''}</p>
        <button class="reinstate-btn" onclick="showReinstateModal('${surveyId}')">Reinstate Survey</button>
        <p><strong>Original ID:</strong> ${survey.original_id || ''}</p>
      </div>
      ${attachmentsHtml}
    `;

    container.appendChild(card);
  }
}

// Reinstate Survey Functions
function showReinstateModal(surveyId) {
  const survey = archiveSurveyDataStore[surveyId];
  if (!survey) {
    alert('Survey not found');
    return;
  }

  currentReinstateArchiveId = survey.id;

  // Populate form with existing data
  document.getElementById('reinstate-pilot').value = survey.pilot || '';
  document.getElementById('reinstate-drone_name').value = survey.drone_name || '';
  document.getElementById('reinstate-property_name').value = survey.property_name || '';
  document.getElementById('reinstate-coordinates').value = survey.coordinates || '';
  document.getElementById('reinstate-gate_code').value = survey.gate_code || '';
  document.getElementById('reinstate-google_sheets_id').value = survey.google_sheets_id || '';
  document.getElementById('reinstate-notes').value = survey.notes || '';
  document.getElementById('reinstate-driving_instructions').value = survey.driving_instructions || '';

  // Set default date to today
  const today = new Date().toISOString().split('T')[0];
  document.getElementById('reinstate-scheduled_time').value = today;

  // Show modal
  document.getElementById('reinstateModal').style.display = 'block';
}

function closeReinstateModal() {
  document.getElementById('reinstateModal').style.display = 'none';
  currentReinstateArchiveId = null;
}

// Handle reinstate form submission
document.addEventListener('DOMContentLoaded', function() {
  document.getElementById('reinstateForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    if (!currentReinstateArchiveId) {
      alert('No survey selected for reinstatement');
      return;
    }

    const formData = new FormData(e.target);
    const scheduledDate = formData.get('scheduled_time');

    // Convert date to proper datetime format (11:59 PM Central Time)
    const scheduledDateTime = localDateToDateTime(scheduledDate);

    const data = {
      action: 'reinstate',
      archive_id: currentReinstateArchiveId,
      new_scheduled_time: scheduledDateTime,
      pilot: formData.get('pilot'),
      drone_name: formData.get('drone_name'),
      property_name: formData.get('property_name'),
      coordinates: formData.get('coordinates'),
      gate_code: formData.get('gate_code'),
      google_sheets_id: formData.get('google_sheets_id'),
      notes: formData.get('notes'),
      driving_instructions: formData.get('driving_instructions')
    };

    // Handle file uploads if provided
    const imageFile = formData.get('image');
    if (imageFile && imageFile.size > 0) {
      if (!imageFile.type.startsWith('image/')) {
        alert('Please upload a valid image file.');
        return;
      }
      const base64Image = await toBase64(imageFile);
      data.image_base64 = base64Image;
    }

    const pdfFile = formData.get('pdf');
    if (pdfFile && pdfFile.size > 0) {
      if (pdfFile.type !== 'application/pdf') {
        alert('Please upload a valid PDF file.');
        return;
      }
      const base64Pdf = await toBase64(pdfFile);
      data.pdf_base64 = base64Pdf;
    }

    try {
      const response = await fetch(API_URL, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': API_KEY
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      await response.json();
      alert('Survey reinstated successfully!');

      // Close modal and refresh the archive list
      closeReinstateModal();

      // Remove the reinstated survey from the current view
      // Find the survey card by looking for the survey ID in the data store
      let surveyIdToRemove = null;
      Object.keys(archiveSurveyDataStore).forEach(key => {
        if (archiveSurveyDataStore[key].id === currentReinstateArchiveId) {
          surveyIdToRemove = key;
        }
      });

      if (surveyIdToRemove) {
        // Find the survey card by looking for the reinstate button with this survey ID
        const archiveContainer = document.getElementById('archive-container');
        const surveyCards = archiveContainer.querySelectorAll('.survey-card');

        surveyCards.forEach(card => {
          const reinstateBtn = card.querySelector(`button[onclick*="showReinstateModal('${surveyIdToRemove}')"]`);
          if (reinstateBtn) {
            card.remove();
          }
        });
      }

      // Clear the survey from data store
      Object.keys(archiveSurveyDataStore).forEach(key => {
        if (archiveSurveyDataStore[key].id === currentReinstateArchiveId) {
          delete archiveSurveyDataStore[key];
        }
      });

    } catch (error) {
      console.error('Error reinstating survey:', error);
      alert(`Error reinstating survey: ${error.message}`);
    }
  });
});
